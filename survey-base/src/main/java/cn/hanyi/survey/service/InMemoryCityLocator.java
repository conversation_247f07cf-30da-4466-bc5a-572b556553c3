package cn.hanyi.survey.service;

import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.FeatureSource;
import org.geotools.feature.FeatureCollection;
import org.geotools.feature.FeatureIterator;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.index.SpatialIndex;
import org.locationtech.jts.index.quadtree.Quadtree;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 基于 GeoTools 的内存空间索引方案
 * 适用于离线场景，全量数据加载到内存并构建索引
 */
public class InMemoryCityLocator {
    // 内存空间索引（四叉树）
    private SpatialIndex spatialIndex;
    // 存储要素ID到城市名称的映射
    private Map<String, String> featureIdToCityName;
    // 存储要素ID到几何对象的映射
    private Map<String, Geometry> featureIdToGeometry;
    // 几何工厂
    private GeometryFactory geometryFactory;

    public InMemoryCityLocator() {
        this.spatialIndex = new Quadtree(); // 使用四叉树索引
        this.featureIdToCityName = new HashMap<>();
        this.featureIdToGeometry = new HashMap<>();
        this.geometryFactory = JTSFactoryFinder.getGeometryFactory();
    }

    /**
     * 从 Shapefile 加载数据并构建内存索引
     * @param shapefilePath Shapefile 文件路径（如 china_cities.shp）
     */
    public void loadData(String shapefilePath) throws IOException {
        File file = new File(shapefilePath);
        Map<String, Object> params = new HashMap<>();
        params.put("url", file.toURI().toURL());

        // 通过 GeoTools 读取 Shapefile
        DataStore dataStore = null;
        try {
            dataStore = DataStoreFinder.getDataStore(params);
            if (dataStore == null) {
                throw new IOException("无法打开 Shapefile: " + shapefilePath);
            }

            String typeName = dataStore.getTypeNames()[0];
            FeatureSource<SimpleFeatureType, SimpleFeature> featureSource = dataStore.getFeatureSource(typeName);
            FeatureCollection<SimpleFeatureType, SimpleFeature> features = featureSource.getFeatures();

            // 遍历要素并构建索引
            try (FeatureIterator<SimpleFeature> iterator = features.features()) {
                int count = 0;
                while (iterator.hasNext()) {
                    SimpleFeature feature = iterator.next();
                    // 获取要素ID（唯一标识）
                    String featureId = feature.getID();
                    // 获取城市名称（假设属性名为"name"，需根据实际数据调整）
                    String cityName = feature.getAttribute("id").toString();
                    // 获取几何边界（城市边界）
                    Geometry geometry = (Geometry) feature.getDefaultGeometry();

                    // 存储映射关系
                    featureIdToCityName.put(featureId, cityName);
                    featureIdToGeometry.put(featureId, geometry);

                    // 将要素的包围盒添加到空间索引
                    Envelope envelope = geometry.getEnvelopeInternal();
                    spatialIndex.insert(envelope, featureId);

                    count++;
                }
                System.out.println("成功加载 " + count + " 个城市数据，索引构建完成");
            }
        } finally {
            // 手动关闭 DataStore
            if (dataStore != null) {
                dataStore.dispose();
            }
        }
    }

    /**
     * 根据经纬度查询所在城市
     * @param latitude 纬度
     * @param longitude 经度
     * @return 城市名称，未找到则返回"未知城市"
     */
    public String getCity(double latitude, double longitude) {
        // 创建点几何对象（注意：经纬度顺序是 x=经度, y=纬度）
        Point point = geometryFactory.createPoint(new Coordinate(longitude, latitude));

        // 1. 通过空间索引快速筛选候选要素ID
        @SuppressWarnings("unchecked")
        java.util.List<String> candidateFeatureIds = spatialIndex.query(point.getEnvelopeInternal());

        // 2. 对候选要素进行精确的几何包含判断
        for (String featureId : candidateFeatureIds) {
            Geometry geometry = featureIdToGeometry.get(featureId);
            if (geometry != null && geometry.contains(point)) {
                return featureIdToCityName.get(featureId);
            }
        }

        return "未知城市";
    }

    // 测试方法
    public static void main(String[] args) {
        try {
            InMemoryCityLocator locator = new InMemoryCityLocator();
            // 替换为实际的 Shapefile 路径
            locator.loadData("/Users/<USER>/Downloads/city.shp");

            // 测试北京坐标 (39.9042° N, 116.4074° E)
            String city = locator.getCity(39.9042, 116.4074);
            System.out.println("北京坐标所在城市: " + city);

            // 测试上海坐标 (31.2304° N, 121.4737° E)
            city = locator.getCity(31.2304, 121.4737);
            System.out.println("上海坐标所在城市: " + city);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

